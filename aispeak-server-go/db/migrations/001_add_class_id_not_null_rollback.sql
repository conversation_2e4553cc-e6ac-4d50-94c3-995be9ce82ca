-- 回滚脚本：将 tasks 表的 class_id 字段改回允许 NULL
-- 创建时间：2024-05-28
-- 描述：如果需要回滚 class_id NOT NULL 约束

-- 第一步：检查当前状态
DESCRIBE tasks;

-- 第二步：修改列约束，允许 NULL
ALTER TABLE tasks 
MODIFY COLUMN class_id VARCHAR(80) NULL COMMENT '班级ID';

-- 第三步：验证回滚结果
DESCRIBE tasks;

-- 第四步：显示当前数据状态
SELECT 
    COUNT(*) as total_tasks,
    COUNT(class_id) as tasks_with_class_id,
    COUNT(*) - COUNT(class_id) as tasks_without_class_id
FROM tasks;
