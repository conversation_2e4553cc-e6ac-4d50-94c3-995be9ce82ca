-- 迁移脚本：将 tasks 表的 class_id 字段设置为 NOT NULL
-- 创建时间：2024-05-28
-- 描述：确保所有任务都必须关联到班级

-- 第一步：检查现有数据
SELECT 
    COUNT(*) as total_tasks,
    COUNT(class_id) as tasks_with_class_id,
    COUNT(*) - COUNT(class_id) as tasks_without_class_id
FROM tasks;

-- 第二步：为 class_id 为 NULL 的记录设置默认值
-- 注意：在生产环境中，应该根据业务逻辑设置正确的 class_id
UPDATE tasks 
SET class_id = 'UNKNOWN_CLASS' 
WHERE class_id IS NULL OR class_id = '';

-- 第三步：修改列约束为 NOT NULL
ALTER TABLE tasks 
MODIFY COLUMN class_id VARCHAR(80) NOT NULL COMMENT '班级ID';

-- 第四步：添加索引（如果不存在）
-- 检查索引是否存在
SELECT COUNT(*) as index_exists
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND index_name = 'idx_tasks_class_id';

-- 如果索引不存在，创建索引
-- 注意：这个查询需要根据上面的结果手动执行
-- CREATE INDEX idx_tasks_class_id ON tasks(class_id);

-- 第五步：验证修改结果
DESCRIBE tasks;

-- 第六步：检查数据完整性
SELECT 
    COUNT(*) as total_tasks,
    COUNT(DISTINCT class_id) as unique_classes,
    COUNT(DISTINCT teacher_id) as unique_teachers
FROM tasks;

-- 显示可能需要手动处理的记录
SELECT id, title, teacher_id, class_id, created_at
FROM tasks 
WHERE class_id = 'UNKNOWN_CLASS'
ORDER BY created_at DESC;
