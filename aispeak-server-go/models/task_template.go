package models

import (
	"gorm.io/gorm"
)

// TaskTemplate 任务模板模型 - 可复用的任务定义
// @Description 任务模板，用于创建标准化的任务内容
type TaskTemplate struct {
	gorm.Model
	
	// 模板标题
	Title string `gorm:"type:varchar(200);not null;comment:模板标题"`
	
	// 模板描述
	Description string `gorm:"type:text;comment:模板描述"`
	
	// 任务类型
	TaskType TaskType `gorm:"type:varchar(50);not null;index;comment:任务类型"`
	
	// 所属学科
	Subject SubjectType `gorm:"type:varchar(50);not null;index;comment:所属学科"`
	
	// 模板创建者ID
	CreatorID string `gorm:"type:varchar(80);not null;index;comment:创建者ID"`
	
	// 是否为公共模板（可被其他教师使用）
	IsPublic bool `gorm:"default:false;comment:是否公共模板"`
	
	// 模板状态
	Status TemplateStatus `gorm:"type:varchar(20);default:'draft';comment:模板状态"`
	
	// 预估完成时间（分钟）
	EstimatedDuration int `gorm:"comment:预估完成时间"`
	
	// 难度等级
	DifficultyLevel DifficultyLevel `gorm:"type:varchar(20);comment:难度等级"`
	
	// 适用年级
	GradeLevel string `gorm:"type:varchar(50);comment:适用年级"`
	
	// 标签（用于分类和搜索）
	Tags JSON `gorm:"type:json;comment:标签"`
	
	// 模板内容
	TemplateContents []TaskContentTemplate `gorm:"foreignKey:TemplateID;references:ID"`
}

// TaskContentTemplate 任务内容模板
type TaskContentTemplate struct {
	gorm.Model
	
	// 关联模板ID
	TemplateID uint `gorm:"not null;index;comment:模板ID"`
	
	// 内容类型
	ContentType string `gorm:"type:varchar(50);not null;comment:内容类型"`
	
	// 分值
	Points int `gorm:"not null;comment:分值"`
	
	// 排序
	OrderNum int `gorm:"not null;comment:排序"`
	
	// 生成模式
	GenerateMode string `gorm:"type:varchar(20);not null;comment:生成模式"`
	
	// 教材相关
	RefBookID string `gorm:"type:varchar(100);comment:教材ID"`
	RefLessonID int `gorm:"comment:课程ID"`
	
	// 预选内容（用于manual模式）
	SelectedWordIDs []int32 `gorm:"type:json;comment:预选单词ID"`
	SelectedSentenceIDs []int32 `gorm:"type:json;comment:预选句子ID"`
	
	// 内容配置
	ContentConfig JSON `gorm:"type:json;comment:内容配置"`
}

// TaskAssignment 任务分配记录
// @Description 将任务模板分配给具体的教师和班级
type TaskAssignment struct {
	gorm.Model
	
	// 关联模板ID（可选，直接创建的任务此字段为空）
	TemplateID *uint `gorm:"index;comment:模板ID"`
	
	// 分配给的教师ID
	TeacherID string `gorm:"type:varchar(80);not null;index;comment:教师ID"`
	
	// 分配给的班级ID
	ClassID string `gorm:"type:varchar(80);not null;index;comment:班级ID"`
	
	// 任务标题（可以覆盖模板标题）
	Title string `gorm:"type:varchar(200);not null;comment:任务标题"`
	
	// 任务描述（可以覆盖模板描述）
	Description string `gorm:"type:text;comment:任务描述"`
	
	// 截止时间
	Deadline *time.Time `gorm:"comment:截止时间"`
	
	// 任务状态
	Status TaskStatus `gorm:"type:varchar(20);default:'draft';index;comment:任务状态"`
	
	// 是否允许迟交
	AllowLateSubmission bool `gorm:"default:false;comment:允许迟交"`
	
	// 最大尝试次数
	MaxAttempts *int `gorm:"comment:最大尝试次数"`
	
	// 分配者ID（可能不是教师本人）
	AssignedBy string `gorm:"type:varchar(80);not null;comment:分配者ID"`
	
	// 自定义配置（覆盖模板配置）
	CustomConfig JSON `gorm:"type:json;comment:自定义配置"`
	
	// 关联的实际任务内容
	AssignmentContents []TaskAssignmentContent `gorm:"foreignKey:AssignmentID;references:ID"`
}

// TaskAssignmentContent 分配任务的具体内容
type TaskAssignmentContent struct {
	gorm.Model
	
	// 关联分配ID
	AssignmentID uint `gorm:"not null;index;comment:分配ID"`
	
	// 关联模板内容ID（如果基于模板）
	TemplateContentID *uint `gorm:"index;comment:模板内容ID"`
	
	// 内容类型
	ContentType string `gorm:"type:varchar(50);not null;comment:内容类型"`
	
	// 分值
	Points int `gorm:"not null;comment:分值"`
	
	// 排序
	OrderNum int `gorm:"not null;comment:排序"`
	
	// 实际使用的内容ID
	SelectedWordIDs []int32 `gorm:"type:json;comment:选中的单词ID"`
	SelectedSentenceIDs []int32 `gorm:"type:json;comment:选中的句子ID"`
	
	// 元数据
	Metadata JSON `gorm:"type:json;comment:元数据"`
}

// 枚举类型定义
type TemplateStatus string

const (
	TemplateStatusDraft     TemplateStatus = "draft"     // 草稿
	TemplateStatusActive    TemplateStatus = "active"    // 激活
	TemplateStatusArchived  TemplateStatus = "archived"  // 归档
)

type DifficultyLevel string

const (
	DifficultyEasy   DifficultyLevel = "easy"   // 简单
	DifficultyMedium DifficultyLevel = "medium" // 中等
	DifficultyHard   DifficultyLevel = "hard"   // 困难
)

// 添加时间导入
import "time"
